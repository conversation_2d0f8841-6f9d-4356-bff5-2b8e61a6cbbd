import 'package:cq_youzan_webview/cq_youzan_controller.dart';
import 'package:cq_youzan_webview_example/youzan_webview_page.dart';
import 'package:flutter/material.dart';
import 'dart:async';

import 'package:flutter/services.dart';
import 'package:cq_youzan_webview/cq_youzan_webview.dart';

void main() {
  runApp(const MyApp());
}


class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: DemoPage(),
    );
  }
}


class DemoPage extends StatefulWidget {
  const DemoPage({super.key});

  @override
  State<DemoPage> createState() => _DemoPageState();
}

class _DemoPageState extends State<DemoPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('有赞测试'),
      ),
      body: Column(
        children: [
          ElevatedButton(
            child: Text('初始化有赞'),
            onPressed: () {
              CQYouZanController.initYZSdk(appKey: 'be23e0ff7b7946c38eb7db2d918e1794', clientId: '3de7355c8465687071', iOSScheme: 'schemeios');
            },
          ),
          ElevatedButton(
            child: Text('登录有赞'),
            onPressed: () {
              CQYouZanController.login(userId: '1616655761617822', nickName: '预先森1', avatar: 'https://img2.baidu.com/it/u=3435341269,3572684113&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500');
            },
          ),
          ElevatedButton(
            child: Text('退出有赞'),
            onPressed: () {
              CQYouZanController.logout();
            },
          ),
          ElevatedButton(
            child: Text('加载链接'),
            onPressed: () {
              String url = 'https://shop153971273.youzan.com/v2/showcase/homepage?alias=vC1uZ46aH1&dc_ps=3900048604399832065.300001';
              Navigator.of(context).push(MaterialPageRoute(builder: (_) {
                return YouZanWebviewPage(url: url, title: '有赞');
              }));
            },
          ),
        ],
      ),
    );
  }
}


