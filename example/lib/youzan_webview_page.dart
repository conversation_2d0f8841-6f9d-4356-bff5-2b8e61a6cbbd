import 'package:cq_youzan_webview/cq_youzan_controller.dart';
import 'package:cq_youzan_webview/cq_youzan_webview.dart';
import 'package:flutter/material.dart';

class YouZanWebviewPage extends StatefulWidget {
  final String url;
  final String title;

  const YouZanWebviewPage({
    super.key,
    required this.url,
    required this.title,
  });

  @override
  State<YouZanWebviewPage> createState() => _YouZanWebviewPageState();
}

class _YouZanWebviewPageState extends State<YouZanWebviewPage> {
  late CQYouZanController _controller;

  void _loadUrl() {
    _controller.loadUrl(url: widget.url);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('有赞页面')),
      floatingActionButton: FloatingActionButton(onPressed: () async {
        bool canGoBack = await _controller.canGoBack();
        debugPrint('canGoBack = $canGoBack');
        if(canGoBack) {
          _controller.goBack();
        }
      }),
      body: CQYouZanWebView(
        onCreated: (controller) {
          _controller = controller;
          _loadUrl();
        },
      ),
    );
  }
}
