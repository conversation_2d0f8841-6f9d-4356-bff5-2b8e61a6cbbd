//
//  CQYouZanWebViewController.m
//  cq_youzan_webview
//
//  Created by ybx on 2025/7/30.
//

#import "CQYouZanWebViewController.h"
#import "CQYouZanWebView.h"
#import "CQYouZanDefine.h"


@interface CQYouZanWebViewController()<YZWebViewNoticeDelegate, YZWebViewDelegate>

@property(nonatomic, strong) CQYouZanWebView *youzanWebView;

/// viewID标识
@property(nonatomic, assign) int64_t viewId;

/// 事件通道
@property(nonatomic, strong) FlutterMethodChannel *channel;

@end

@implementation CQYouZanWebViewController


-(instancetype)initWithWithFrame:(CGRect)frame viewIdentifier:(int64_t)viewId arguments:(id)args binaryMessenger:(NSObject<FlutterBinaryMessenger> *)messenger {
    if(self = [super init]){
        
        
        // 初始化 有赞webview
        self.youzanWebView = [[CQYouZanWebView alloc] initWithFrame:self.view.bounds];
        self.youzanWebView.backgroundColor = [UIColor whiteColor];
        self.youzanWebView.webView.noticeDelegate = self;
        self.youzanWebView.webView.delegate = self;
        
        self.viewId = viewId;
        NSLog(@"self.viewId = %lld", viewId);
        
        NSString *channelName = [NSString stringWithFormat:@"%@_%lld", CQYouZanWebViewChannelName, viewId];
        self.channel = [FlutterMethodChannel methodChannelWithName:channelName binaryMessenger:messenger];
        
        __weak __typeof__(self) weakSelf = self;
        [self.channel setMethodCallHandler:^(FlutterMethodCall *  call, FlutterResult  result) {
            [weakSelf onMethodCall:call result:result];
        }];
        
    }
    return self;
}

-(UIView *)view {
    return self.youzanWebView;
}


-(void)onMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result{
    NSLog(@"%@", [NSString stringWithFormat:@"dart -> native : method = %@, arguments = %@", [call method], call.arguments]);
    
    if ([[call method] isEqualToString:@"loadUrl"]) {
        NSString *url = [NSString stringWithFormat:@"%@", call.arguments[@"url"]];
        [self.youzanWebView.webView loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:url]]];
    } else if([[call method] isEqualToString:@"canGoBack"]){
        result(@([self.youzanWebView.webView canGoBack]));
    } else if([[call method] isEqualToString:@"goBack"]){
        [self.youzanWebView.webView goBack];
        result(@(YES));
    }else {
        result(FlutterMethodNotImplemented);
    }
}

- (void)dealloc {
    NSLog(@"CQYouZanWebViewController -- dealloc");
}

- (void)webView:(nonnull id<YZWebView>)webView didReceiveNotice:(nonnull YZNotice *)notice { 
    if(notice.type == YZNoticeTypeLogin) {
        __weak typeof(self) weakSelf = self;
 
        [[YZSDK shared] loginWithOpenUserId:lastUserId avatar:lastAvatar extra:lastExtra nickName:lastNickName gender:lastGender andCompletion:^(BOOL isSuccess, NSString * _Nullable yzOpenId) {
            NSLog(@"自动登录结果 isSuccess = %@  yzOpenId = %@", (isSuccess ? @"成功" : @"失败"), yzOpenId);

            dispatch_async(dispatch_get_main_queue(), ^{
                [weakSelf.youzanWebView.webView reload];
            });
        }];
    }
}

@end
