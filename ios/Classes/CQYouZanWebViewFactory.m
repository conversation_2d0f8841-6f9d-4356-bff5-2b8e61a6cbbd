//
//  CQYouZanWebViewFactory.m
//  cq_youzan_webview
//
//  Created by ybx on 2025/7/30.
//

#import "CQYouZanWebViewFactory.h"
#import "CQYouZanWebViewController.h"

@implementation CQYouZanWebViewFactory {
    NSObject<FlutterBinaryMessenger> *_messenger;
}

-(instancetype)initWithMessenger:(NSObject<FlutterBinaryMessenger> *)messenger {
    if(self = [super init]){
        _messenger = messenger;
    }
    return self;
}


-(NSObject<FlutterMessageCodec> *)createArgsCodec {
    return [FlutterStandardMessageCodec sharedInstance];
}

-(NSObject<FlutterPlatformView> *)createWithFrame:(CGRect)frame viewIdentifier:(int64_t)viewId arguments:(id)args {
    CQYouZanWebViewController *controller = [[CQYouZanWebViewController alloc] initWithWithFrame:frame viewIdentifier:viewId arguments:args binaryMessenger:_messenger];
    return controller;
}
@end
