#import "CqYouzanWebviewPlugin.h"
#import "CQYouZanDefine.h"
#import <YZBaseSDK/YZBaseSDK.h>
#import "CQYouZanWebViewFactory.h"




@implementation CqYouzanWebviewPlugin
+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
    

  FlutterMethodChannel* channel = [FlutterMethodChannel methodChannelWithName: CQYouZanWebViewNativeChannelName binaryMessenger:[registrar messenger]];
  CqYouzanWebviewPlugin* instance = [[CqYouzanWebviewPlugin alloc] init];
  [registrar addMethodCallDelegate:instance channel:channel];
    
    
    CQYouZanWebViewFactory *factory = [[CQYouZanWebViewFactory alloc] initWithMessenger:registrar.messenger];
    [registrar registerViewFactory:factory withId:CQYouZanWebViewPluginId];
    
}

- (void)handleMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result {
  if ([@"getPlatformVersion" isEqualToString:call.method]) {
    result([@"iOS " stringByAppendingString:[[UIDevice currentDevice] systemVersion]]);
  } else if([@"initYZSdk" isEqualToString:call.method]){
      [self initYouZanSDKWithMethodCall:call];
  } else if([@"login" isEqualToString:call.method]){
      [self loginYouZanSDKWithMethodCall:call];
  } else if([@"logout" isEqualToString:call.method]){
      [self logoutZanSDKWithMethodCall:call];
  } else {
    result(FlutterMethodNotImplemented);
  }
}

- (void) initYouZanSDKWithMethodCall:(FlutterMethodCall*)call {
    NSString *clientId = [NSString stringWithFormat:@"%@", call.arguments[@"clientId"]];
    NSString *appKey = [NSString stringWithFormat:@"%@", call.arguments[@"appKey"]];
    NSString *scheme = [NSString stringWithFormat:@"%@", call.arguments[@"iOSScheme"]];
    NSLog(@"clientId = %@  appKey = %@   scheme = %@", clientId, appKey, scheme);
    YZConfig *config = [[YZConfig alloc] initWithClientId:clientId andAppKey:appKey];
    config.scheme = scheme;
    config.enableLog = YES;
    [YZSDK.shared initializeSDKWithConfig:config];
}

- (void) loginYouZanSDKWithMethodCall:(FlutterMethodCall*)call {
    NSString *userId = [NSString stringWithFormat:@"%@", call.arguments[@"userId"]];
    NSString *avatar = [NSString stringWithFormat:@"%@", call.arguments[@"avatar"]];
    NSString *extra = [NSString stringWithFormat:@"%@", call.arguments[@"extra"]];
    NSString *nickName = [NSString stringWithFormat:@"%@", call.arguments[@"nickName"]];
    int gender = [call.arguments[@"gender"] intValue];
    
    [[YZSDK shared] loginWithOpenUserId:userId avatar:avatar extra:extra nickName:nickName gender:gender andCompletion:^(BOOL isSuccess, NSString * _Nullable yzOpenId) {
        NSLog(@"登录结果 isSuccess = %@  yzOpenId = %@", (isSuccess ? @"成功" : @"失败"), yzOpenId);
        if(isSuccess) {
            lastUserId = userId;
            lastAvatar = avatar;
            lastExtra = extra;
            lastNickName = nickName;
            lastGender = gender;
        }
    }];
}


- (void) logoutZanSDKWithMethodCall:(FlutterMethodCall*)call {

    [[YZSDK shared] logoutWithCompletion:^{
        NSLog(@"退出成功");
        lastUserId = @"";
        lastAvatar = @"";
        lastExtra = @"";
        lastNickName = @"";
        lastGender = 0;
    }];
}



@end
