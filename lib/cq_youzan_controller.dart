import 'package:flutter/services.dart';

class CQYouZanController {
  static final _nativeChannel = const MethodChannel('plugin.cq.youzan_webview_native_channel');

  MethodChannel? _methodChannel;

  /// 初始化有赞webview
  /// [clientId] clientId
  /// [appKey] appKey
  /// [iOSScheme] 设置有赞调起支付时的 scheme，注意，原生端也要配置
  static Future<void> initYZSdk({
    required String clientId,
    required String appKey,
    String iOSScheme = '',
  }) async {
    _nativeChannel.invokeMethod('initYZSdk', {
      'clientId': clientId,
      'appKey': appKey,
      'iOSScheme': iOSScheme,
    });
  }

  /// 登录有赞
  /// [userId] 用户id
  /// [avatar] 头像
  /// [nickName] 昵称
  /// [extra] 扩展字符串
  static Future login({
    required String userId,
    String avatar = '',
    String extra = '',
    String nickName = '',
    int gender = 0,
  }) async {
    _nativeChannel.invokeMethod('login', {
      'userId': userId,
      'avatar': avatar,
      'extra': extra,
      'nickName': nickName,
      'gender': gender,
    });
  }

  /// 退出有赞登录状态
  static Future logout() async {
    _nativeChannel.invokeMethod('logout');
  }

  static CQYouZanController init(int id) {
    CQYouZanController result = CQYouZanController();
    result._methodChannel = MethodChannel("plugin.cq.youzan_webview_controller_channel_$id");
    result._methodChannel?.setMethodCallHandler(result._methodCallHandler);
    return result;
  }

  Future _invokeMethod(String method, [dynamic arguments]) async {
    return await _methodChannel?.invokeMethod(method, arguments);
  }

  /// native 调用 dart 方法的回调
  Future<dynamic> _methodCallHandler(MethodCall call) {
    return Future.value();
  }

  /// 加载有赞链接
  void loadUrl({required String url}) async {
    _invokeMethod('loadUrl', {"url": url});
  }

  Future<bool> canGoBack() async {
    return await _invokeMethod('canGoBack');
  }

  Future goBack() async {
    _invokeMethod('goBack');
  }
}
