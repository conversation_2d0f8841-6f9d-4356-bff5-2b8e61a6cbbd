
import 'dart:io';
import 'package:cq_youzan_webview/cq_youzan_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';


const String viewTypeString = 'plugins.cq.platform_youzan_webview';
typedef CQYouZanWebViewCreatedCallBack = void Function(CQYouZanController controller);

class CQYouZanWebView extends StatefulWidget {
  final CQYouZanWebViewCreatedCallBack onCreated;
  const CQYouZanWebView({super.key, required this.onCreated});

  @override
  State<CQYouZanWebView> createState() => _CQYouZanWebViewState();
}

class _CQYouZanWebViewState extends State<CQYouZanWebView> {
  @override
  Widget build(BuildContext context) {
    if (Platform.isAndroid) {
      return AndroidView(
        viewType: viewTypeString,
        onPlatformViewCreated: onPlatformViewCreated,
        creationParamsCodec: const StandardMessageCodec(),
      );
    } else {
      return UiKitView(
        viewType: viewTypeString,
        onPlatformViewCreated: onPlatformViewCreated,
        creationParamsCodec: const StandardMessageCodec(),
      );
    }
  }

  void onPlatformViewCreated(int id) {
    CQYouZanController controller = CQYouZanController.init(id);
    widget.onCreated(controller);
  }
}
